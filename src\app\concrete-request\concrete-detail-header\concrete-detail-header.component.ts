/* eslint-disable max-lines-per-function */
import {
  Component, Input, OnDestroy, OnInit, TemplateRef, ViewChild,
} from '@angular/core';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { ToastrService } from 'ngx-toastr';
import { Router } from '@angular/router';
import { Socket } from 'ngx-socket-io';
import { UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';
import moment from 'moment';
import { Subscription } from 'rxjs';
import { DeliveryService } from '../../services/profile/delivery.service';
import { ProjectService } from '../../services/profile/project.service';
import { EditConcreteRequestsComponent } from '../edit-concrete-requests/edit-concrete-requests.component';
import { hoursDropdown } from '../../services/hoursDropdown';
import { minutesDropdown } from '../../services/minutesDropdown';
import { MixpanelService } from '../../services/mixpanel.service';

@Component({
  selector: 'app-concrete-detail-header',
  templateUrl: './concrete-detail-header.component.html',
})
export class ConcreteDetailHeaderComponent implements OnInit, OnDestroy {
  @Input() data: any;

  @Input() title: string;

  public currentTabId = 0;

  public ProjectId: any;

  public ParentCompanyId: any;

  public ConcreteRequestId: any;

  public voidSubmitted = false;

  public statusSubmitted = false;

  public skipped = false;

  public currentDeliverySaveItem: any = {};

  public statusChanged = false;

  public showSaveButton = false;

  public showButtons = false;

  public otherForm: UntypedFormGroup;

  public modalRef1: BsModalRef;

  public modalRef2: BsModalRef;

  public hoursDropdown = hoursDropdown;

  public minutesDropdown = minutesDropdown;

  public currentTabHeading: any;

  public statusValue: any = ['Approved', 'Declined'];

  public currentStatus = '';

  public authUser: any = {};

  public showStatus = false;

  public show = false;

  public void = false;

  public modalRef3: BsModalRef;

  public gatesubmit = false;

  public memberList: any = [];

  public seriesOptions = [];

  public allRequestIsOpened = false;

  @ViewChild('personConfirmationPopup') public personModal: TemplateRef<any>;

  private readonly concreteRequestSubscription: Subscription;


  public constructor(
    private readonly modalService: BsModalService,
    public modalRef: BsModalRef,
    public deliveryService: DeliveryService,
    public socket: Socket,
    public bsModalRef: BsModalRef,
    private readonly mixpanelService: MixpanelService,
    private readonly formBuilder: UntypedFormBuilder,
    public router: Router,
    public toastr: ToastrService,
    public projectService: ProjectService,
  ) {
    this.deliveryService.loginUser.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.authUser = res;
        this.getConcreteRequest();
      }
    });
    this.projectService.projectParent.subscribe((projectResponse20): void => {
      if (
        projectResponse20 !== undefined
        && projectResponse20 !== null
        && projectResponse20 !== ''
      ) {
        this.ProjectId = projectResponse20.ProjectId;
        this.ParentCompanyId = projectResponse20.ParentCompanyId;
      }
    });
    this.concreteRequestSubscription = this.deliveryService.getCurrentConcreteRequestStatus.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.ConcreteRequestId = res;
        this.getConcreteRequest();
      }
    });
    this.otherDetailsForm();
    this.getMembers();
  }

  public selectStatus(status: string): void {
    this.currentStatus = status;
    this.statusChanged = true;
  }

  public ngOnInit(): void {
    this.setStatus();
    this.currentTabId = 0;
    this.router.events.subscribe((e): void => {
      this.bsModalRef.hide();
    });
    this.socket.on('getConcreteCommentHistory', (commenthistoryresponse: any): void => {
      if (commenthistoryresponse?.message === 'Concrete Booking Comment added successfully.') {
        this.currentTabId = 2;
      }
    });
    this.socket.on(
      'getConcreteAttachmentDeleteHistory',
      (attachmentdeletehistoryresponse: any): void => {
        if (
          attachmentdeletehistoryresponse?.message
          === 'Concrete Booking Attachment Deleted Successfully.'
        ) {
          this.currentTabId = 1;
        }
      },
    );
    this.socket.on('getConcreteApproveHistory', (NDRapprovehistoryresponse: any): void => {
      if (NDRapprovehistoryresponse?.message === 'Uploaded Successfully.') {
        this.currentTabId = 1;
      }
    });
  }

  public onSelect(value): void {
    this.currentTabHeading = value.heading;
    const concreteselectRoleid = this.currentDeliverySaveItem?.createdUserDetails?.RoleId;

    // Add null safety checks
    if (!this.authUser || !this.currentDeliverySaveItem) {
      this.showSaveButton = false;
      return;
    }

    if (this.authUser.RoleId === 4) {
      if (
        concreteselectRoleid === this.authUser.RoleId
        && value.heading === 'Details'
        && (this.currentDeliverySaveItem.status === 'Approved'
          || this.currentDeliverySaveItem.status === 'Expired')
      ) {
        this.showSaveButton = true;
      } else if (
        concreteselectRoleid !== this.authUser.RoleId
        && value.heading === 'Details'
        && (this.currentDeliverySaveItem.status === 'Approved'
          || this.currentDeliverySaveItem.status === 'Expired')
      ) {
        this.showSaveButton = false;
      } else {
        this.showSaveButton = false;
      }
    } else if (
      value.heading === 'Details'
      && (this.currentDeliverySaveItem.status === 'Approved'
        || this.currentDeliverySaveItem.status === 'Expired')
    ) {
      this.showSaveButton = true;
    } else {
      this.showSaveButton = false;
    }
  }

  public setStatus(): void {
    const deliveryData = this.data;

    // Debug logging to identify the issue
    console.log('ConcreteDetailHeaderComponent - setStatus called with data:', deliveryData);

    // Add null safety check
    if (!deliveryData) {
      console.error('ConcreteDetailHeaderComponent - No data provided to setStatus');
      return;
    }

    this.ParentCompanyId = deliveryData.ParentCompanyId;
    this.ProjectId = deliveryData.ProjectId;
    this.ConcreteRequestId = deliveryData.id;

    console.log('ConcreteDetailHeaderComponent - Extracted values:', {
      ParentCompanyId: this.ParentCompanyId,
      ProjectId: this.ProjectId,
      ConcreteRequestId: this.ConcreteRequestId
    });

    if (this.ConcreteRequestId !== -1 && this.ConcreteRequestId !== undefined && this.ConcreteRequestId !== null) {
      console.log('ConcreteDetailHeaderComponent - Updating EditConcreteRequestId with:', this.ConcreteRequestId);
      this.deliveryService.updatedEditConcreteRequestId(this.ConcreteRequestId);
      this.getConcreteRequest();
    } else {
      console.error('ConcreteDetailHeaderComponent - Invalid ConcreteRequestId:', this.ConcreteRequestId);
    }
  }

  public changeRequestCollapse(data): void {
    this.initializeSeriesOption();

    // Add null safety check
    if (data?.concretePlacementStart && !moment(data.concretePlacementStart).isAfter(moment())) {
      this.seriesOptions = this.seriesOptions.filter((object): any => {
        const seriesObject = object;
        if (seriesObject.option !== 1) {
          seriesObject.disabled = true;
        }
        return seriesObject;
      });
    }
    this.allRequestIsOpened = !this.allRequestIsOpened;
  }

  public initializeSeriesOption(): void {
    this.seriesOptions = [
      {
        option: 1,
        text: 'This event',
        disabled: false,
      },
      {
        option: 2,
        text: 'This and all following events',
        disabled: false,
      },
    ];
  }

  public getConcreteRequest(): void {
    const param = {
      ConcreteRequestId: this.ConcreteRequestId,
      ParentCompanyId: this.ParentCompanyId,
      ProjectId: this.ProjectId,
    };
    this.void = false;
    this.show = false;
    if (this.ProjectId && this.ParentCompanyId) {
      this.deliveryService.getConcreteRequestDetail(param).subscribe((res): void => {
        // Add null safety check for response data
        if (!res?.data) {
          this.currentDeliverySaveItem = { edit: false };
          return;
        }

        this.currentDeliverySaveItem = res.data;
        this.currentDeliverySaveItem.edit = false;
        const item = this.currentDeliverySaveItem;
        const authId = this.authUser?.id;
        this.checkRoleStatus(res, authId);

        if (this.currentDeliverySaveItem.status === 'Tentative' && this.authUser?.RoleId === 2) {
          this.showStatus = true;
        } else {
          this.showStatus = false;
        }
        if (this.authUser?.RoleId === 2) {
          if (
            (this.currentDeliverySaveItem.status === 'Approved'
              || this.currentDeliverySaveItem.status === 'Expired')
            && this.currentTabHeading === 'Details'
          ) {
            this.showSaveButton = true;
          }
        }
        if (this.authUser?.RoleId === 3) {
          if (
            this.currentDeliverySaveItem.status === 'Approved'
            && this.currentTabHeading === 'Details'
          ) {
            this.showSaveButton = true;
          }
        }
        if (this.authUser?.RoleId === 4) {
          this.checkRole4(item);
        }
      });
    }
  }

  public checkRole4(item) {
    const loggedInuserId = this.authUser.UserId;
    const concreteCreatedUserId = item.createdUserDetails.User.id;
    const concreteRoleid = item.createdUserDetails.RoleId;
    if (
      this.currentDeliverySaveItem.status === 'Approved'
      && this.currentTabHeading === 'Details'
      && this.authUser.RoleId === 4
      && concreteCreatedUserId === loggedInuserId
      && concreteRoleid === this.authUser.RoleId
    ) {
      this.showSaveButton = true;
    } else if (
      this.currentDeliverySaveItem.status === 'Approved'
      && this.currentTabHeading === 'Details'
      && this.authUser.RoleId === 4
      && concreteCreatedUserId === loggedInuserId
      && this.currentDeliverySaveItem.createdUserDetails.RoleId !== this.authUser.RoleId
    ) {
      this.showSaveButton = false;
    }
  }

  public checkRoleStatus(res, authId) {
    if (this.authUser.RoleId === 4 || this.authUser.RoleId === 3) {
      const newMember = res.data.memberDetails;
      const index = newMember.findIndex(
        (i: { Member: { id: any } }): boolean => i.Member.id === this.authUser.id,
      );
      if (index !== -1) {
        this.currentDeliverySaveItem.edit = true;
      } else {
        this.currentDeliverySaveItem.edit = false;
      }
    } else {
      this.currentDeliverySaveItem.edit = true;
    }

    const voidData = this.currentDeliverySaveItem.voidList;
    if (voidData) {
      const voidIndex = voidData.findIndex(
        (i: { MemberId: any }): boolean => i.MemberId === authId,
      );
      if (voidIndex !== -1) {
        this.void = true;
      }
    }
    this.show = true;
  }

  public closeModal(): void {
    this.modalRef.hide();
    this.currentTabHeading = '';
    this.showSaveButton = false;
  }

  public openEditModal(item, action): void {
    if (this.modalRef) {
      this.modalRef.hide();
    }
    const className = 'modal-lg new-delivery-popup custom-modal';
    this.modalRef = this.modalService.show(EditConcreteRequestsComponent, {
      backdrop: 'static',
      keyboard: false,
      class: className,
      initialState: {
        seriesoption: item?.recurrence && item?.recurrence?.recurrence !== 'Does Not Repeat' ? action : 1,
        recurrenceId: item?.recurrence?.id || null,
        recurrenceEndDate: item?.recurrence?.recurrenceEndDate || null,
      },
    });
    this.deliveryService.updatedEditConcreteRequestId(this.ConcreteRequestId);
    this.modalRef.content.closeBtnName = 'Close';
    this.modalRef.content.seriesOption = item?.recurrence && item?.recurrence?.recurrence !== 'Does Not Repeat' ? action : 1;
    this.modalRef.content.recurrenceId = item?.recurrence ? item?.recurrence?.id : null;
    this.modalRef.content.recurrenceEndDate = item?.recurrence
      ? item?.recurrence?.recurrenceEndDate
      : null;
  }

  public clickAndDisable(link): void {
    // disable subsequent clicks
    const clickDisable = link;
    clickDisable.onclick = (event): void => {
      event.preventDefault();
    };
  }

  public openModal(template: TemplateRef<any>): void {
    let data = {};
    data = {
      keyboard: false,
      class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
    };
    this.modalRef = this.modalService.show(template, data);
  }

  public openModal1(template: TemplateRef<any>): void {
    let data = {};
    data = {
      keyboard: false,
      class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
    };
    this.modalRef1 = this.modalService.show(template, data);
  }

  public openModal2(template: TemplateRef<any>): void {
    let data = {};
    data = {
      keyboard: false,
      class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
    };
    this.modalRef2 = this.modalService.show(template, data);
  }

  public openModal3(template: TemplateRef<any>): void {
    let data = {};
    data = { backdrop: 'static', keyboard: false, class: 'modal-md thanks-popup custom-modal' };
    this.modalRef = this.modalService.show(template, data);
    this.modalRef1.hide();
  }

  public ngAfterViewInit(): void {
    this.deliveryService.showFooterButtons.subscribe((res): void => {
      if (res) {
        this.showButtons = true;
      } else {
        this.showButtons = false;
      }
    });
  }

  public getMembers(): void {
    const param = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .getRegisteredMember(param)
      .subscribe((response: any): void => {
        if (response) {
          this.memberList = response.data;
        }
      });
  }

  public gatestatus(action: string): void {
    this.gatesubmit = false;
    if (action === 'yes') {
      this.gatesubmit = true;
      const data = {
        id: this.currentDeliverySaveItem.id,
        status: 'Completed',
        ParentCompanyId: this.ParentCompanyId,
        hoursToCompletePlacement: this.otherForm.value.hoursToCompletePlacement,
        minutesToCompletePlacement: this.otherForm.value.minutesToCompletePlacement,
        cubicYardsTotal: this.otherForm.value.cubicYardsTotal,
      };
      this.deliveryService.updateConcreteRequestStatus(data).subscribe({
        next: (updateStatusResponse2: any): void => {
          if (updateStatusResponse2) {
            this.gatesubmit = false;
            this.modalRef.hide();
            this.deliveryService.completeConcreteRequestStatus(false);
            this.toastr.success(updateStatusResponse2.message, 'Success');
            this.socket.emit('ConcreteApproveHistory', updateStatusResponse2);
            this.deliveryService.updateConcreteRequestHistory(
              { status: true },
              'ConcreteApproveHistory',
            );
            this.mixpanelService.addMixpanelEvents('Completed Concrete Booking');
            this.statusSubmitted = false;
            this.skipped = false;
            this.currentDeliverySaveItem.status = 'Completed';
            this.statusChanged = false;
            this.showButtons = false;
            this.showSaveButton = false;
            if (this.modalRef1) {
              this.modalRef1.hide();
            }
          }
        },
        error: (newNDRCreateErr): void => {
          this.deliveryService.completeConcreteRequestStatus(false);
          this.statusSubmitted = false;
          this.skipped = false;
          this.statusChanged = false;
          this.showButtons = false;
          this.showSaveButton = false;
          if (newNDRCreateErr.message?.statusCode === 400) {
            this.showError(newNDRCreateErr);
          } else if (!newNDRCreateErr.message) {
            this.toastr.error('Try again later.!', 'Something went wrong.');
          } else {
            this.toastr.error(newNDRCreateErr.message, 'OOPS!');
            this.modalRef.hide();
            if (this.modalRef1) {
              this.modalRef1.hide();
            }
          }
        },
      });
    } else {
      if (this.modalRef) {
        this.modalRef.hide();
      }
      this.bsModalRef.hide();
      const className = 'modal-lg new-delivery-popup custom-modal';
      this.modalRef3 = this.modalService.show(EditConcreteRequestsComponent, {
        backdrop: 'static',
        keyboard: false,
        class: className,
      });
      this.modalRef3.content.closeBtnName = 'Close';
    }
  }

  public savestatus1(): void {
    if (!this.currentStatus) {
      this.toastr.clear();
      this.toastr.error('No status chosen to save');
      this.modalRef.hide();
      return;
    }
    if (!this.statusSubmitted) {
      this.statusSubmitted = true;
      const data = {
        id: this.currentDeliverySaveItem.id,
        status: this.currentStatus,
        ParentCompanyId: this.ParentCompanyId,
      };
      const arrytest = this.currentDeliverySaveItem.memberDetails.map((detail) => detail.Member);

      const arr1 = this.memberList;
      const arr2 = arrytest;
      let index2: number;
      const result = arr1.filter((o): any => arr2.some(({ id }): any => o.id === id));
      if (arrytest.length !== result.length) {
        index2 = -1;
      } else {
        index2 = 0;
      }
      if (index2 === -1) {
        this.openModal3(this.personModal);
      } else {
        this.deliveryService.updateConcreteRequestStatus(data).subscribe({
          next: (updateStatusResponse2: any): void => {
            if (updateStatusResponse2) {
              this.toastr.success(updateStatusResponse2.message, 'Success');
              this.mixpanelService.addMixpanelEvents(`${this.currentStatus} Concrete Booking`);
              this.showStatus = false;
              this.deliveryService.completeConcreteRequestStatus(false);
              this.socket.emit('ConcreteApproveHistory', updateStatusResponse2);
              this.deliveryService.updateConcreteRequestHistory(
                { status: true },
                'ConcreteApproveHistory',
              );
              this.statusSubmitted = false;
              this.currentDeliverySaveItem.status = this.currentStatus;
              this.statusChanged = false;
            }
          },
          error: (newNDRCreateErr): void => {
            this.statusSubmitted = false;
            this.statusChanged = false;
            if (newNDRCreateErr.message?.statusCode === 400) {
              this.showError(newNDRCreateErr);
            } else if (!newNDRCreateErr.message) {
              this.toastr.error('Try again later.!', 'Something went wrong.');
            } else {
              this.toastr.error(newNDRCreateErr.message, 'OOPS!');
              this.modalRef.hide();
            }
          },
        });
      }
    }
  }

  public saveStatus(action): void {
    this.statusChanged = true;
    if (!this.statusSubmitted || !this.skipped) {
      const arrytest = this.currentDeliverySaveItem.memberDetails.map((detail) => detail.Member);
      const arr1 = this.memberList;
      const arr2 = arrytest;
      let index2: number;
      const result = arr1.filter((o) => arr2.some(({ id }) => o.id === id));
      if (arrytest.length !== result.length) {
        index2 = -1;
      } else {
        index2 = 0;
      }
      if (index2 === -1) {
        this.openModal3(this.personModal);
      } else {
        if (action === 'skip') {
          this.skipped = true;
        }
        if (action === 'submit') {
          this.statusSubmitted = true;
        }
        const data = {
          id: this.currentDeliverySaveItem.id,
          status: 'Completed',
          ParentCompanyId: this.ParentCompanyId,
          hoursToCompletePlacement: this.otherForm.value.hoursToCompletePlacement,
          minutesToCompletePlacement: this.otherForm.value.minutesToCompletePlacement,
          cubicYardsTotal: this.otherForm.value.cubicYardsTotal,
        };
        this.deliveryService.updateConcreteRequestStatus(data).subscribe({
          next: (updateStatusResponse2: any): void => {
            if (updateStatusResponse2) {
              this.deliveryService.completeConcreteRequestStatus(false);
              this.toastr.success(updateStatusResponse2.message, 'Success');
              this.socket.emit('ConcreteApproveHistory', updateStatusResponse2);
              this.deliveryService.updateConcreteRequestHistory(
                { status: true },
                'ConcreteApproveHistory',
              );
              this.mixpanelService.addMixpanelEvents('Completed Concrete Booking');
              this.statusSubmitted = false;
              this.skipped = false;
              this.currentDeliverySaveItem.status = 'Completed';
              this.statusChanged = false;
              this.showButtons = false;
              this.showSaveButton = false;
              if (this.modalRef1) {
                this.modalRef1.hide();
              }
            }
          },
          error: (newNDRCreateErr): void => {
            this.deliveryService.completeConcreteRequestStatus(false);
            this.statusSubmitted = false;
            this.skipped = false;
            this.statusChanged = false;
            this.showButtons = false;
            this.showSaveButton = false;
            if (newNDRCreateErr.message?.statusCode === 400) {
              this.showError(newNDRCreateErr);
            } else if (!newNDRCreateErr.message) {
              this.toastr.error('Try again later.!', 'Something went wrong.');
            } else {
              this.toastr.error(newNDRCreateErr.message, 'OOPS!');
              this.modalRef.hide();
              if (this.modalRef1) {
                this.modalRef1.hide();
              }
            }
          },
        });
      }
    }
  }

  public voidConfirmationResponse(action): void {
    if (action === 'no') {
      this.modalRef2.hide();
    } else {
      if (this.modalRef2) {
        this.modalRef2.hide();
      }
      this.addToVoid();
    }
  }

  public showError(err): void {
    let errorMessage: any = '';
    errorMessage = Object.values(err.message.details[0]);
    this.toastr.error(errorMessage);
  }

  public addToVoid(): void {
    const newData = { ProjectId: this.ProjectId, ParentCompanyId: this.ParentCompanyId };
    this.projectService.updateAccountProjectParent(newData);
    if (!this.voidSubmitted) {
      this.voidSubmitted = true;
      const currentDeliveryItem = this.currentDeliverySaveItem;
      const data = {
        ConcreteRequestId: currentDeliveryItem.id,
        ProjectId: this.ProjectId,
        ParentCompanyId: this.ParentCompanyId,
      };
      this.deliveryService.addConcreteRequestToVoid(data).subscribe({
        next: (response: any): void => {
          if (response) {
            this.toastr.success(response.message, 'Success');
            this.voidSubmitted = false;
            this.deliveryService.updatedHistory({ status: true }, 'AddedToVoid');
            this.deliveryService.updateConcreteRequestHistory({ status: true }, 'AddedToVoid');
            this.mixpanelService.addMixpanelEvents('Concrete Booking Voided');
            if (this.bsModalRef) {
              this.bsModalRef.hide();
            }
          }
        },
        error: (createVoidError): void => {
          this.voidSubmitted = false;
          if (createVoidError.message?.statusCode === 400) {
            this.showError(createVoidError);
          } else if (!createVoidError.message) {
            this.toastr.error('Try again later.!', 'Something went wrong.');
          } else {
            this.toastr.error(createVoidError.message, 'OOPS!');
          }
        },
      });
    }
  }

  public handleDownKeydown(event: KeyboardEvent, data: any, item: any, type: any) {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      switch (type) {
        case 'edit':
          this.openEditModal(data, item);
          break;
        case 'role':
          this.savestatus1();
          break;
        default:
          break;
      }
    }
  }

  public otherDetailsForm(): void {
    this.otherForm = this.formBuilder.group({
      hoursToCompletePlacement: [''],
      minutesToCompletePlacement: [''],
      cubicYardsTotal: [''],
    });
  }

  public revertstatus(template: TemplateRef<any>): void {
    if (this.authUser.RoleId === 1 || this.authUser.RoleId === 2) {
      let data = {};
      data = {
        keyboard: false,
        class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
      };
      this.modalRef2 = this.modalService.show(template, data);
    }
  }

  public statusupdate(action: string): void {
    if (action === 'no') {
      this.modalRef2.hide();
    } else {
      this.statusSubmitted = true;
      if (this.modalRef2) {
        this.modalRef2.hide();
      }
      const data = {
        id: this.currentDeliverySaveItem.id,
        status: 'Approved',
        ParentCompanyId: this.ParentCompanyId,
        statuschange: 'Reverted',
      };
      this.deliveryService
        .updateConcreteRequestStatus(data)
        .subscribe((updateStatusResponse2: any): void => {
          if (updateStatusResponse2) {
            this.statusSubmitted = false;
            this.toastr.success('Status Reverted Successfully', 'Success');
            this.showStatus = false;
            this.deliveryService.completeConcreteRequestStatus(false);
            this.socket.emit('ConcreteApproveHistory', updateStatusResponse2);
            this.deliveryService.updateConcreteRequestHistory(
              { status: true },
              'ConcreteApproveHistory',
            );
            this.statusSubmitted = false;
            this.currentDeliverySaveItem.status = this.currentStatus;
            this.statusChanged = false;
          }
        });
    }
  }

  ngOnDestroy(): void {
    if (this.concreteRequestSubscription) {
      this.concreteRequestSubscription.unsubscribe();
    }
  }
}
