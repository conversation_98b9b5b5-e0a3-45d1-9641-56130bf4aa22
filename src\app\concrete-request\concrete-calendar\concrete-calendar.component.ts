/* eslint-disable no-underscore-dangle */
/* eslint-disable max-lines-per-function */
import { Component, ViewChild, TemplateRef } from '@angular/core';
import { FullCalendarComponent } from '@fullcalendar/angular';
import { CalendarOptions } from '@fullcalendar/core';
import interactionPlugin from '@fullcalendar/interaction';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { Title } from '@angular/platform-browser';
import { ToastrService } from 'ngx-toastr';
import { Router } from '@angular/router';
import moment from 'moment';
import { UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';
import { DeliveryService } from '../../services/profile/delivery.service';
import { CalendarService } from '../../services/profile/calendar.service';
import { AddConcreteRequestComponent } from '../add-concrete-request/add-concrete-request.component';
import { ProjectService } from '../../services/profile/project.service';
import { ConcreteDetailHeaderComponent } from '../concrete-detail-header/concrete-detail-header.component';

@Component({
  selector: 'app-concrete-calendar',
  templateUrl: './concrete-calendar.component.html',
})
export class ConcreteCalendarComponent {
  public events: any = [];

  public modalRef: BsModalRef;

  public currentView = 'Month';

  public showSearchbar = false;

  public modalLoader = false;

  public ProjectId;

  public ParentCompanyId;

  public filterCount = 0;

  public calendarApi;

  public Range: any = {};

  public filterForm: UntypedFormGroup;

  public modalRef1: BsModalRef;

  public wholeStatus = ['Approved', 'Completed', 'Expired', 'Tentative'];

  public modalRef2: BsModalRef;

  public locationDropdown: [];

  public locationDetailsDropdown: [];

  public mixDesignDropdown: [];

  public concreteSupplierDropdown: any[];

  public search = '';

  public approved: string;

  public pending: string;

  public rejected: string;

  public delivered: string;

  public expired: string;

  @ViewChild('fullcalendar', { static: true }) public calendarComponent1: FullCalendarComponent;

  public calendarOptions: CalendarOptions = {
    selectable: true,
    initialView: 'dayGridMonth',
    plugins: [interactionPlugin, dayGridPlugin, timeGridPlugin],
    aspectRatio: 2,
    expandRows: true,
    nowIndicator: true,
    dayMaxEvents: true,
    contentHeight: 'liquid',
    fixedWeekCount: false,
    slotEventOverlap: false,
    moreLinkClick: 'popover',
    showNonCurrentDates: false,
    customButtons: {
      prev: {
        text: 'PREV',
        click: (): void => this.goPrev(),
      },
      next: {
        text: 'Next',
        click: (): void => this.goNext(),
      },
      prevYear: {
        text: 'PREV',
        click: (): void => this.goPrevYear(),
      },
      nextYear: {
        text: 'Next',
        click: (): void => this.goNextYear(),
      },
      timeGridWeek: {
        text: 'Week',
        click: (): void => this.goTimeGridWeekOrDay('timeGridWeek'),
      },
      timeGridDay: {
        text: 'Day',
        click: (): void => this.goTimeGridWeekOrDay('timeGridDay'),
      },
      dayGridMonth: {
        text: 'Month',
        click: (): void => this.goDayGridMonth(),
      },
    },
    headerToolbar: {
      right: '',
      center: 'prevYear,prev,title,next,nextYear',
      left: 'dayGridMonth,timeGridWeek,timeGridDay',
    },
    firstDay: 0,
    eventTimeFormat: {
      hour: 'numeric',
      minute: '2-digit',
      meridiem: 'short',
    },
    eventClick: (arg): void => {
      this.openRequestDetail(arg);
    },
    events: this.events,
    eventDidMount(info): void {
      const argument = info;
      const line1 = argument.event._def.extendedProps.description;
      const { line2 } = argument.event._def.extendedProps;
      const eventTitleElement: HTMLElement = argument.el.querySelector('.fc-event-title');
      eventTitleElement.style.backgroundColor = info.backgroundColor;
      eventTitleElement.style.color = info.textColor;
      eventTitleElement.style.borderLeft = `4px solid ${info.borderColor}`;
      // eslint-disable-next-line no-param-reassign
      if (argument.event._def.allDay) {
        eventTitleElement.innerHTML = `
            <div class="d-flex flex-column">
            <p class="m-0"> <img  class="w10 h10" src="./assets/images/noun-event-alert.svg" class="form-icon" alt="allday" >  ${line1} </p>
            <small> ${line2}</small>
            </div>`;
      } else {
        // eslint-disable-next-line no-param-reassign
        eventTitleElement.innerHTML = `
          <div class="d-flex flex-column">
          <p class="m-0"> ${line1} </p>
          <small> ${line2}</small>
          </div>`;
      }
    },
    dayMaxEventRows: true, // for all non-TimeGrid views
    views: {
      timeGrid: {
        dayMaxEventRows: 2, // adjust to 6 only for timeGridWeek/timeGridDay
      },
      dayGridMonth: {
        allDaySlot: true,
      },
      timeGridWeek: {
        allDaySlot: true,
      },
      timeGridDay: {
        allDaySlot: true,
      },
    },
    dateClick: (info): void => {
      this.openAddConcreteRequestModal(info);
    },
  };

  public selectedTagItems = [];

  public isDisabled = true;

  public loader = true;

  public concreteRequest: any[];

  public descriptionPopup: boolean;

  public calendarDescriptionPopup = false;

  public viewEventData: any;

  public message = '';

  public currentViewMonth: moment.MomentInput;

  public monthlyEventloader = true;

  public monthEvents = [];

  public subscription: any;

  public subscription1: any;

  public constructor(
    private readonly modalService: BsModalService,
    private readonly router: Router,
    public projectService: ProjectService,
    private readonly toastr: ToastrService,
    public deliveryService: DeliveryService,
    private readonly formBuilder: UntypedFormBuilder,
    private readonly calendarService: CalendarService,
    private readonly titleService: Title,
  ) {
    this.titleService.setTitle('Follo - Concrete Calendar');
    this.projectService.projectParent.subscribe((response): void => {
      if (response !== undefined && response !== null && response !== '') {
        this.ParentCompanyId = response.ParentCompanyId;
        this.ProjectId = response.ProjectId;
      }
    });
    this.deliveryService.fetchConcreteData.subscribe((getEventNDR): void => {
      if (getEventNDR !== undefined && getEventNDR !== null && getEventNDR !== '') {
        this.getConcreteRequest();
      }
    });
    this.deliveryService.fetchConcreteData1.subscribe((getEventNDR): void => {
      if (getEventNDR !== undefined && getEventNDR !== null && getEventNDR !== '') {
        this.getConcreteRequest();
      }
    });
  }

  public goNext(): void {
    this.closeDescription();
    this.calendarApi.next();
    this.setCalendar();
  }

  public setCalendar(): void {
    this.calendarApi = this.calendarComponent1.getApi();
    this.Range = this.calendarApi?.currentData?.dateProfile.activeRange;
    this.getConcreteRequest();
  }

  public openAddConcreteRequestModal(dateArg): void {
    const passData = {
      date: dateArg.dateStr,
      currentView: this.currentView,
    };
    const initialState = {
      data: passData,
      title: 'Modal with component',
    };
    const className = 'modal-lg new-delivery-popup custom-modal';
    this.modalRef = this.modalService.show(AddConcreteRequestComponent, {
      backdrop: 'static',
      keyboard: false,
      class: className,
      initialState,
    });
    this.modalRef.content.closeBtnName = 'Close';
  }

  public openRequestDetail(arg): void {
    this.calendarDescriptionPopup = false;
    let getConcreteRequest: any = {};
    if (Object.keys(arg).length !== 0) {
      let index: number;
      if (arg.event.extendedProps.uniqueNumber) {
        index = this.concreteRequest.findIndex(
          (item: { id: any; uniqueNumber: any }): any => item.id === +arg.event.id && item.uniqueNumber === arg.event.extendedProps.uniqueNumber,
        );
      } else {
        index = this.concreteRequest.findIndex((item): any => +item.id === +arg.event.id);
      }
      getConcreteRequest = this.concreteRequest[index];

      // Debug logging to identify the issue
      console.log('ConcreteCalendarComponent - openRequestDetail:', {
        arg: arg,
        index: index,
        getConcreteRequest: getConcreteRequest,
        concreteRequestArray: this.concreteRequest
      });

      if (getConcreteRequest.requestType === 'calendarEvent') {
        this.calendarDescription(arg);
      } else {
        const className = 'modal-lg new-delivery-popup custom-modal';
        const newPayload = {
          id: getConcreteRequest.ConcreteRequestId,
          ProjectId: getConcreteRequest.ProjectId,
          ParentCompanyId: this.ParentCompanyId,
        };

        console.log('ConcreteCalendarComponent - newPayload created:', newPayload);

        const initialState = {
          data: newPayload,
          title: 'Modal with component',
        };
        this.deliveryService.updatedCurrentConcreteRequestStatus(+getConcreteRequest.ConcreteRequestId);
        this.modalRef1 = this.modalService.show(ConcreteDetailHeaderComponent, {
          backdrop: 'static',
          keyboard: false,
          class: className,
          initialState,
        });
        this.modalRef1.content.closeBtnName = 'Close';
      }
    }
  }

  public ngAfterViewInit(): void {
    this.projectService.projectParent.subscribe((response): void => {
      if (response !== undefined && response !== null && response !== '') {
        this.ParentCompanyId = response.ParentCompanyId;
        this.ProjectId = response.ProjectId;
        this.setCalendar();
        this.filterDetailsForm();
        this.getDropdownValue();
      }
    });
  }

  // eslint-disable-next-line max-lines-per-function
  public getConcreteRequest(): void {
    this.loader = true;
    this.concreteRequest = [];
    const filterParams = {
      ProjectId: this.ProjectId,
      void: 0,
    };
    let getConcreteRequestPayload: any = {};
    if (this.filterForm !== undefined) {
      getConcreteRequestPayload = {
        locationFilter: this.filterForm.value.locationFilter,
        locationPathFilter: this.filterForm.value.locationPathFilter,
        descriptionFilter: this.filterForm.value.descriptionFilter,
        statusFilter: this.filterForm.value.statusFilter,
        mixDesignFilter: this.filterForm.value.mixDesignFilter,
        orderNumberFilter: this.filterForm.value.orderNumberFilter,
        concreteSupplierFilter: this.filterForm.value.concreteSupplierFilter,
      };
    }
    getConcreteRequestPayload.search = this.search;
    getConcreteRequestPayload.ParentCompanyId = this.ParentCompanyId;
    getConcreteRequestPayload.start = moment(this.Range?.start).format('YYYY-MM-DD');
    getConcreteRequestPayload.end = moment(this.Range?.end).format('YYYY-MM-DD');
    getConcreteRequestPayload.filterCount = this.filterCount;
    getConcreteRequestPayload.calendarView = this.currentView;
    this.calendarService
      .getConcreteRequest(filterParams, getConcreteRequestPayload)
      .subscribe((NdrResponse: any): void => {
        this.getDropdownValue();
        if (NdrResponse) {
          this.loader = false;
          this.concreteRequest = NdrResponse?.data;
          const statusData = JSON.parse(NdrResponse.statusData.statusColorCode);
          const cardData = JSON.parse(NdrResponse.cardData.concreteCard);
          const UseTextColorAsLegendColor = JSON.parse(NdrResponse.statusData.useTextColorAsLegend);
          const isDefaultColor = JSON.parse(NdrResponse.statusData.isDefaultColor);
          const approved = statusData.find((item) => item.status === 'approved');
          const pending = statusData.find((item) => item.status === 'pending');
          const delivered = statusData.find((item) => item.status === 'delivered');
          const rejected = statusData.find((item) => item.status === 'rejected');
          const expired = statusData.find((item) => item.status === 'expired');

          if (UseTextColorAsLegendColor) {
            this.approved = approved.fontColor;
            this.pending = pending.fontColor;
            this.expired = expired.fontColor;
            this.rejected = rejected.fontColor;
            this.delivered = delivered.fontColor;
          } else {
            this.approved = approved.backgroundColor;
            this.pending = pending.backgroundColor;
            this.expired = expired.backgroundColor;
            this.rejected = rejected.backgroundColor;
            this.delivered = delivered.backgroundColor;
          }
          if (isDefaultColor) {
            this.delivered = delivered.backgroundColor;
          }
          this.events = [];
          const previewSelected = cardData.filter((item): any => item.selected);
          this.concreteRequest.forEach((element): any => {
            const assignData: any = {
              description: '',
              title: '',
              start: '',
              end: '',
              id: '',
              uniqueNumber: '',
              allDay: false,
              allDaySlot: false,
              line2: '',
            };
            assignData.start = element.requestType === 'concreteRequest'
              ? element.concretePlacementStart
              : element.fromDate;
            assignData.end = element.requestType === 'concreteRequest'
              ? element.concretePlacementEnd
              : element.toDate;

            if (element.requestType === 'calendarEvent') {
              assignData.description = element.description;
              assignData.title = element.description;
            }
            assignData.id = element.id;
            assignData.uniqueNumber = element.uniqueNumber;
            if (element.requestType === 'calendarEvent' && element.isAllDay === true) {
              delete assignData.allDay;
              delete assignData.allDaySlot;
              assignData.allDay = true;
              assignData.allDaySlot = true;
              assignData.start = element.fromDate;
              assignData.end = element.toDate;
            }


            if (element.requestType === 'concreteRequest') {
              previewSelected.forEach((item): void => {
                let value: string | undefined;

                switch (item.label) {
                  case 'Description':
                    value = element.description;
                    break;

                  case 'Responsible Person': {
                    const member = element?.memberDetails?.[0]?.Member?.User;
                    value = member ? `${member.firstName} ${member.lastName}` : '';
                    break;
                  }

                  case 'Concrete Supplier':
                    value = element.concreteSupplierDetails?.[0]?.Company?.companyName;
                    break;

                  case 'Concrete Request ID':
                    value = element.ConcreteRequestId;
                    break;

                  case 'Location':
                    value = element.locationDetails?.[0]?.ConcreteLocation?.location;
                    break;

                  default:
                    return;
                }

                if (item.line === 1) {
                  assignData.description = value;
                } else if (item.line === 2) {
                  assignData.line2 = value;
                }
              });
            }

            if (element.status === 'Tentative') {
              assignData.color = pending.backgroundColor;
              assignData.textColor = pending.fontColor;
              assignData.borderColor = pending.fontColor;
              this.events.push(assignData);
            } else if (element.status === 'Approved') {
              assignData.color = approved.backgroundColor;
              assignData.textColor = approved.fontColor;
              assignData.borderColor = approved.fontColor;
              this.events.push(assignData);
            } else if (element.status === 'Declined') {
              assignData.color = rejected.backgroundColor;
              assignData.textColor = rejected.fontColor;
              assignData.borderColor = rejected.fontColor;
              this.events.push(assignData);
            } else if (element.status === 'Expired') {
              assignData.color = expired.backgroundColor;
              assignData.textColor = expired.fontColor;
              assignData.borderColor = expired.fontColor;
              this.events.push(assignData);
            } else if (element.status === 'Completed') {
              assignData.color = delivered.backgroundColor;
              assignData.textColor = delivered.fontColor;
              assignData.borderColor = delivered.backgroundColor;
              this.events.push(assignData);
            } else if (!element.status) {
              assignData.className = 'calendar_event';
              this.events.push(assignData);
            }
          });
          this.calendarApi.removeAllEventSources();
          this.calendarApi.addEventSource(this.events);
        }
      });
  }

  public openFilterModal(template: TemplateRef<any>): void {
    let data = {};
    data = { backdrop: 'static', keyboard: false, class: 'modal-sm filter-popup custom-modal' };
    this.modalRef = this.modalService.show(template, data);
  }

  public getDropdownValue(): any {
    this.modalLoader = true;
    if (this.ProjectId) {
      const payload = {
        ProjectId: this.ProjectId,
        ParentCompanyId: this.ParentCompanyId,
      };
      this.deliveryService.getConcreteRequestDropdownData(payload).subscribe((response): void => {
        if (response.data) {
          this.locationDropdown = response.data.locationDropdown;
          this.locationDetailsDropdown = response.data.locationDetailsDropdown.filter((item : any) => item.location !== '' && item.location !== null);
          this.concreteSupplierDropdown = response.data.concreteSupplierDropdown;
          this.mixDesignDropdown = response.data.mixDesignDropdown;
          this.modalLoader = false;
        }
      });
    }
  }

  public goTimeGridWeekOrDay(view): void {
    if (view === 'timeGridWeek') {
      this.currentView = 'Week';
    } else {
      this.currentView = 'Day';
    }
    this.closeDescription();
    this.calendarApi.changeView(view);
    this.setCalendar();
  }

  public handleDownKeydown(event: KeyboardEvent, data: any, type: any) {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      switch (type) {
        case 'clear':
          this.clear();
          break;
        case 'close':
          this.closeCalendarDescription();
          break;
        case 'filter':
          this.openFilterModal(data);
          break;

        default:
          break;
      }
    }
  }

  public goPrevYear(): void {
    this.closeDescription();
    this.calendarApi.prevYear(); // call a method on the Calendar object
    this.setCalendar();
  }

  public goNextYear(): void {
    this.closeDescription();
    this.calendarApi.nextYear(); // call a method on the Calendar object
    this.setCalendar();
  }

  public goDayGridMonth(): void {
    this.currentView = 'Month';
    this.closeDescription();
    this.calendarApi.changeView('dayGridMonth');
    this.setCalendar();
  }

  public goPrev(): void {
    this.closeDescription();
    this.calendarApi.prev(); // call a method on the Calendar object
    this.setCalendar();
  }

  public closeDescription(): void {
    this.descriptionPopup = false;
  }

  public openModal(template: TemplateRef<any>): any {
    const className = 'modal-lg new-delivery-popup custom-modal';
    this.modalRef = this.modalService.show(template, { class: className });
  }

  public openAddModal(): void {
    const className = 'modal-lg new-delivery-popup custom-modal';
    this.modalRef = this.modalService.show(AddConcreteRequestComponent, {
      backdrop: 'static',
      keyboard: false,
      class: className,
    });
  }

  public resetFilter(): void {
    this.filterCount = 0;
    this.filterForm.reset();
    this.search = '';
    this.filterDetailsForm();
    this.getConcreteRequest();
    this.modalRef.hide();
  }

  public filterSubmit(): void {
    this.filterCount = 0;
    if (this.filterForm.get('descriptionFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('locationFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('locationPathFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('orderNumberFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('concreteSupplierFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('statusFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('mixDesignFilter').value !== '') {
      this.filterCount += 1;
    }
    this.getConcreteRequest();
    this.modalRef.hide();
  }

  public getSearch(data: any): void {
    if (data.length > 0) {
      this.showSearchbar = true;
    } else {
      this.showSearchbar = false;
    }
    this.search = data;
    this.getConcreteRequest();
  }

  public clear(): void {
    this.showSearchbar = false;
    this.search = '';
    this.getConcreteRequest();
  }

  public filterDetailsForm(): void {
    this.filterForm = this.formBuilder.group({
      descriptionFilter: [''],
      locationFilter: [''],
      locationPathFilter: [''],
      concreteSupplierFilter: [''],
      orderNumberFilter: [''],
      statusFilter: [''],
      mixDesignFilter: [''],
    });
  }

  public calendarDescription(arg: any): void {
    this.calendarDescriptionPopup = false;
    this.descriptionPopup = false;
    this.viewEventData = '';
    if (Object.keys(arg).length !== 0) {
      const index = this.events.findIndex(
        (item: any): any => item.description === arg.event.title
          && item.uniqueNumber === arg.event.extendedProps.uniqueNumber,
      );
      this.viewEventData = this.concreteRequest[index];
      this.occurMessage(this.viewEventData);
      this.calendarDescriptionPopup = true;
    }
  }

  public closeCalendarDescription(): void {
    this.calendarDescriptionPopup = false;
    this.descriptionPopup = false;
    this.viewEventData = '';
  }

  public occurMessage(data: {
    repeatEveryType: string;
    repeatEveryCount: string | number;
    days: any[];
    chosenDateOfMonth: any;
    dateOfMonth: any;
    monthlyRepeatType: any;
    endTime: moment.MomentInput;
  }): void {
    this.message = 'Occurs every day';
    if (data.repeatEveryType === 'Day') {
      this.message = '';
      this.message = 'Occurs every day';
    }
    if (data.repeatEveryType === 'Days') {
      this.message = '';
      if (+data.repeatEveryCount === 2) {
        this.message = 'Occurs every other day';
      } else {
        this.message = `Occurs every ${data.repeatEveryCount} days`;
      }
    }
    if (data.repeatEveryType === 'Week') {
      this.message = '';
      let weekDays = '';
      data.days.forEach((day1: any): any => {
        weekDays = `${weekDays + day1},`;
        return false;
      });
      weekDays = weekDays.replace(/,\s*$/, '');
      this.message += `Occurs every ${weekDays}`;
    }
    if (data.repeatEveryType === 'Weeks') {
      let weekDays = '';
      data.days.forEach((day1: any): any => {
        weekDays = `${weekDays + day1},`;
        return false;
      });
      weekDays = weekDays.replace(/,\s*$/, '');
      this.message = '';
      if (+data.repeatEveryCount === 2) {
        this.message = `Occurs every other  ${weekDays}`;
      } else {
        this.message = `Occurs every ${data.repeatEveryCount} weeks on ${weekDays}`;
      }
    }
    if (
      data.repeatEveryType === 'Month'
      || data.repeatEveryType === 'Months'
      || data.repeatEveryType === 'Year'
      || data.repeatEveryType === 'Years'
    ) {
      if (data.chosenDateOfMonth) {
        this.message = `Occurs on day ${data.dateOfMonth}`;
      } else {
        this.message = `Occurs on the ${data.monthlyRepeatType}`;
      }
    }
    this.message += ` until ${moment(data.endTime).format('MM-DD-YYYY')}`;
  }

  public changeFormat(fromDate: any): any {
    if (fromDate) {
      const dayFormat = moment(new Date(fromDate)).format('ddd MM/DD/YYYY');
      return dayFormat;
    }
  }
}
